{"Version": 1, "WorkspaceRootPath": "D:\\Projects\\Code\\CowpilotCapture\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\encryption\\bmddecryptor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\encryption\\bmddecryptor.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\packets\\itemsdropped.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\packets\\itemsdropped.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\models\\droppeditem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\models\\droppeditem.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\models\\itemdata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\models\\itemdata.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\packets\\packet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\packets\\packet.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapturehook\\hookinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|solutionrelative:cowpilotcapturehook\\hookinterface.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|D:\\Projects\\Code\\CowpilotCapture\\cowpilotcapturehook\\hookentrypoint.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|solutionrelative:cowpilotcapturehook\\hookentrypoint.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapturehook\\packetargs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|solutionrelative:cowpilotcapturehook\\packetargs.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\encryption\\simplemoduluscrypto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\encryption\\simplemoduluscrypto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\encryption\\packetcrypto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\encryption\\packetcrypto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\encryption\\numberconversionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\encryption\\numberconversionextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\encryption\\counter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\encryption\\counter.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\encryption\\xor32crypto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\encryption\\xor32crypto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\encryption\\simplemoduluskeys.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\encryption\\simplemoduluskeys.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|d:\\projects\\code\\cowpilotcapture\\cowpilotcapture\\encryption\\packetextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A80AB7AE-05CE-4C0B-BA69-2EEC038EF348}|CowpilotCapture\\CowpilotCapture.csproj|solutionrelative:cowpilotcapture\\encryption\\packetextensions.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|D:\\Projects\\Code\\CowpilotCapture\\cowpilotcapturehook\\socketinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{005E06DD-D772-4EFA-BCFF-640A7243666E}|CowpilotCaptureHook\\CowpilotCaptureHook.csproj|solutionrelative:cowpilotcapturehook\\socketinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 246, "SelectedChildIndex": 10, "Children": [{"$type": "Document", "DocumentIndex": 3, "Title": "DroppedItem.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Models\\DroppedItem.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Models\\DroppedItem.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Models\\DroppedItem.cs", "RelativeToolTip": "CowpilotCapture\\Models\\DroppedItem.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAB8AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T17:36:08.11Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "ItemData.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Models\\ItemData.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Models\\ItemData.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Models\\ItemData.cs", "RelativeToolTip": "CowpilotCapture\\Models\\ItemData.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABgAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T17:13:12.179Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ItemsDropped.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Packets\\ItemsDropped.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Packets\\ItemsDropped.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Packets\\ItemsDropped.cs", "RelativeToolTip": "CowpilotCapture\\Packets\\ItemsDropped.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwB4AAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T15:12:29.476Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "Packet.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Packets\\Packet.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Packets\\Packet.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Packets\\Packet.cs", "RelativeToolTip": "CowpilotCapture\\Packets\\Packet.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAqwCAAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T14:54:26.424Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "PacketArgs.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\PacketArgs.cs", "RelativeDocumentMoniker": "CowpilotCaptureHook\\PacketArgs.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\PacketArgs.cs", "RelativeToolTip": "CowpilotCaptureHook\\PacketArgs.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAwAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T19:57:22.919Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "Xor32Crypto.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\Xor32Crypto.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Encryption\\Xor32Crypto.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\Xor32Crypto.cs", "RelativeToolTip": "CowpilotCapture\\Encryption\\Xor32Crypto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAARAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T19:02:56.251Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "SimpleModulusKeys.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\SimpleModulusKeys.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Encryption\\SimpleModulusKeys.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\SimpleModulusKeys.cs", "RelativeToolTip": "CowpilotCapture\\Encryption\\SimpleModulusKeys.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T19:02:52.583Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "SimpleModulusCrypto.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\SimpleModulusCrypto.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Encryption\\SimpleModulusCrypto.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\SimpleModulusCrypto.cs", "RelativeToolTip": "CowpilotCapture\\Encryption\\SimpleModulusCrypto.cs", "ViewState": "AgIAAK8BAAAAAAAAAAAqwA8AAABYAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T19:02:47.389Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "PacketCrypto.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\PacketCrypto.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Encryption\\PacketCrypto.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\PacketCrypto.cs", "RelativeToolTip": "CowpilotCapture\\Encryption\\PacketCrypto.cs", "ViewState": "AgIAALwAAAAAAAAAAAAcwA0AAAA+AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T19:02:37.441Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "HookInterface.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\HookInterface.cs", "RelativeDocumentMoniker": "CowpilotCaptureHook\\HookInterface.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\HookInterface.cs", "RelativeToolTip": "CowpilotCaptureHook\\HookInterface.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACcAAABeAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T16:46:28.17Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "Program.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Program.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Program.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Program.cs", "RelativeToolTip": "CowpilotCapture\\Program.cs", "ViewState": "AgIAABQAAAAAAAAAAAAQwDMAAAA/AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-22T22:49:15.343Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "BmdDecryptor.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\BmdDecryptor.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Encryption\\BmdDecryptor.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\BmdDecryptor.cs", "RelativeToolTip": "CowpilotCapture\\Encryption\\BmdDecryptor.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAACoAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-24T19:48:07.847Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "NumberConversionExtensions.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\NumberConversionExtensions.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Encryption\\NumberConversionExtensions.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\NumberConversionExtensions.cs", "RelativeToolTip": "CowpilotCapture\\Encryption\\NumberConversionExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAA2AAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T19:02:33.694Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "PacketExtensions.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\PacketExtensions.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Encryption\\PacketExtensions.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\PacketExtensions.cs", "RelativeToolTip": "CowpilotCapture\\Encryption\\PacketExtensions.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAIAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T19:02:43.759Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "Counter.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\Counter.cs", "RelativeDocumentMoniker": "CowpilotCapture\\Encryption\\Counter.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCapture\\Encryption\\Counter.cs", "RelativeToolTip": "CowpilotCapture\\Encryption\\Counter.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAkAAAAoAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-23T19:02:18.716Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "HookEntryPoint.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\HookEntryPoint.cs", "RelativeDocumentMoniker": "CowpilotCaptureHook\\HookEntryPoint.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\HookEntryPoint.cs", "RelativeToolTip": "CowpilotCaptureHook\\HookEntryPoint.cs", "ViewState": "AgIAADkAAAAAAAAAAAAYwBUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T16:43:43.71Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "SocketInfo.cs", "DocumentMoniker": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\SocketInfo.cs", "RelativeDocumentMoniker": "CowpilotCaptureHook\\SocketInfo.cs", "ToolTip": "D:\\Projects\\Code\\CowpilotCapture\\CowpilotCaptureHook\\SocketInfo.cs", "RelativeToolTip": "CowpilotCaptureHook\\SocketInfo.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-21T16:47:50.429Z", "EditorCaption": ""}]}]}]}